"""Test Asset Library Bug Fix.

测试修复 AssetLibrary 中 _clear_assets() 错误清空数据的bug。
在使用 debug_start_hair_dev_direct.cmd 启动环境后运行此脚本。
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

def test_asset_library_bug_fix():
    """测试 AssetLibrary bug 修复。"""
    print("=" * 60)
    print("AssetLibrary Bug 修复测试")
    print("=" * 60)
    
    try:
        # Import required modules
        from cgame_avatar_factory.hair_studio.manager.hair_manager import <PERSON><PERSON>anager
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        
        # Create hair manager
        hair_manager = HairManager()
        print("✓ HairManager 创建成功")
        
        # Create AssetLibrary for card type
        print("\n✓ 创建 Card AssetLibrary...")
        asset_library = AssetLibrary("card", hair_manager)
        print("✓ AssetLibrary 创建成功")
        
        # Check initial state
        print(f"\n✓ 初始状态检查:")
        print(f"  - 资产数量: {len(asset_library.assets)}")
        print(f"  - UI 组件数量: {asset_library.assets_layout.count()}")
        
        if len(asset_library.assets) == 0:
            print("❌ 初始化后没有资产数据!")
            return False
        
        # List assets
        print("✓ 资产列表:")
        for i, asset in enumerate(asset_library.assets):
            print(f"    {i+1}. {asset['name']} (ID: {asset['id']})")
        
        # Test refresh (should not clear data incorrectly)
        print(f"\n✓ 测试 refresh() 方法...")
        original_count = len(asset_library.assets)
        asset_library.refresh()
        
        print(f"  - 刷新前资产数量: {original_count}")
        print(f"  - 刷新后资产数量: {len(asset_library.assets)}")
        print(f"  - 刷新后UI组件数量: {asset_library.assets_layout.count()}")
        
        if len(asset_library.assets) == 0:
            print("❌ refresh() 后资产数据被错误清空!")
            return False
        
        # Test _update_assets_grid (should not clear data)
        print(f"\n✓ 测试 _update_assets_grid() 方法...")
        before_update = len(asset_library.assets)
        asset_library._update_assets_grid()
        
        print(f"  - 更新前资产数量: {before_update}")
        print(f"  - 更新后资产数量: {len(asset_library.assets)}")
        print(f"  - 更新后UI组件数量: {asset_library.assets_layout.count()}")
        
        if len(asset_library.assets) == 0:
            print("❌ _update_assets_grid() 后资产数据被错误清空!")
            return False
        
        # Test search filtering
        print(f"\n✓ 测试搜索过滤功能...")
        asset_library._update_assets_grid(filter_text="basic")
        
        print(f"  - 过滤后资产数量: {len(asset_library.assets)}")
        print(f"  - 过滤后UI组件数量: {asset_library.assets_layout.count()}")
        
        if len(asset_library.assets) == 0:
            print("❌ 搜索过滤后资产数据被错误清空!")
            return False
        
        # Test clear and restore
        print(f"\n✓ 测试清空和恢复...")
        asset_library._update_assets_grid(filter_text="")  # Clear filter
        
        print(f"  - 恢复后资产数量: {len(asset_library.assets)}")
        print(f"  - 恢复后UI组件数量: {asset_library.assets_layout.count()}")
        
        if len(asset_library.assets) != original_count:
            print("❌ 恢复后资产数量不正确!")
            return False
        
        print("\n✅ 所有测试通过!")
        print("✅ AssetLibrary bug 已修复")
        print("✅ 资产数据不会被错误清空")
        print("✅ UI 组件正确显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_all_asset_types():
    """测试所有资产类型。"""
    print("\n" + "=" * 60)
    print("所有资产类型测试")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        
        hair_manager = HairManager()
        
        for asset_type in ["card", "xgen", "curve"]:
            print(f"\n✓ 测试 {asset_type.upper()} 类型...")
            
            # Create AssetLibrary
            asset_library = AssetLibrary(asset_type, hair_manager)
            
            # Check assets
            asset_count = len(asset_library.assets)
            ui_count = asset_library.assets_layout.count()
            
            print(f"  - 资产数量: {asset_count}")
            print(f"  - UI组件数量: {ui_count}")
            
            if asset_count == 0:
                print(f"❌ {asset_type} 类型没有资产!")
                return False
            
            if ui_count == 0:
                print(f"❌ {asset_type} 类型没有UI组件!")
                return False
            
            # List assets
            for asset in asset_library.assets:
                print(f"    * {asset['name']}")
        
        print("\n✅ 所有资产类型测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 资产类型测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_hair_studio_integration():
    """测试与 HairStudioTab 的集成。"""
    print("\n" + "=" * 60)
    print("HairStudioTab 集成测试")
    print("=" * 60)
    
    try:
        from cgame_avatar_factory.hair_studio.ui.hair_studio_tab import HairStudioTab
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        # Create HairStudioTab
        hair_manager = HairManager()
        hair_studio_tab = HairStudioTab(hair_manager)
        print("✓ HairStudioTab 创建成功")
        
        # Test each tab
        tab_names = ["Card", "XGen", "Curve"]
        for i, tab_name in enumerate(tab_names):
            print(f"\n✓ 测试 {tab_name} 标签页...")
            
            # Switch to tab
            hair_studio_tab.setCurrentIndex(i)
            current_tab = hair_studio_tab.get_current_tab()
            
            # Check assets
            asset_count = len(current_tab.asset_library.assets)
            ui_count = current_tab.asset_library.assets_layout.count()
            
            print(f"  - 资产数量: {asset_count}")
            print(f"  - UI组件数量: {ui_count}")
            
            if asset_count == 0:
                print(f"❌ {tab_name} 标签页没有资产!")
                
                # Try manual refresh
                print("✓ 尝试手动刷新...")
                current_tab.refresh_asset_library()
                
                new_asset_count = len(current_tab.asset_library.assets)
                new_ui_count = current_tab.asset_library.assets_layout.count()
                
                print(f"  - 刷新后资产数量: {new_asset_count}")
                print(f"  - 刷新后UI组件数量: {new_ui_count}")
                
                if new_asset_count == 0:
                    print(f"❌ {tab_name} 标签页刷新后仍然没有资产!")
                    return False
            
            # List some assets
            assets_to_show = min(3, len(current_tab.asset_library.assets))
            for j in range(assets_to_show):
                asset = current_tab.asset_library.assets[j]
                print(f"    * {asset['name']}")
        
        print("\n✅ HairStudioTab 集成测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数。"""
    print("🔧 AssetLibrary Bug 修复验证")
    print("请确保已使用 debug_start_hair_dev_direct.cmd 启动环境")
    print()
    
    # Run tests
    tests = [
        ("AssetLibrary Bug 修复", test_asset_library_bug_fix),
        ("所有资产类型", test_all_asset_types),
        ("HairStudioTab 集成", test_hair_studio_integration),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("\n" + "=" * 60)
    print("修复验证总结")
    print("=" * 60)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ AssetLibrary bug 已修复")
        print("✅ 资产数据正确显示")
        print("✅ UI 组件正常工作")
        print("✅ 毛发素材库应该现在可以看到资产了!")
    else:
        print(f"❌ 部分测试失败 ({passed}/{total} 通过)")
        print("请检查上述错误信息")
    
    print("\n测试完成。")

if __name__ == "__main__":
    main()
