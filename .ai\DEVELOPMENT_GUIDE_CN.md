# Maya + PyQt 开发指南

## 1. 开发环境设置
- 使用 maya2020 时使用 Python 2.7
- 使用 maya2022 时使用 Python 3.7
- 确保 rez 环境正确配置
- 使用 Qt.py 作为 Qt 封装层，确保跨 DCC 兼容性

## 2. 组件使用指南

### 2.1 列表组件

#### MListView
`MListView` 是 `dayu_widgets` 提供的列表视图组件，用于显示可滚动的项目列表。

**基本用法：**

```python
from dayu_widgets import MListView
from qtpy.QtWidgets import QVBoxLayout

# 创建列表视图
list_view = MListView()

# 添加项目
list_view.addItems(["Item 1", "Item 2", "Item 3"])

# 连接项目点击信号
list_view.itemClicked.connect(self._on_item_clicked)

# 设置选择模式
list_view.setSelectionMode(QtWidgets.QAbstractItemView.SingleSelection)

# 获取选中项目
selected_items = list_view.selectedItems()
if selected_items:
    print(selected_items[0].text())
```

**与 QListWidget 的区别：**
- `MListView` 是 `dayu_widgets` 的组件，具有统一的外观和主题
- 自动支持暗黑/亮色主题切换
- 提供更现代的 UI 风格
- 更好的性能优化，特别是处理大量项目时

**注意事项：**
- 使用 `MListView` 而不是 `MListWidget`
- 确保正确导入：`from dayu_widgets import MListView`
- 可以通过 `setViewMode()` 方法切换不同的视图模式

## 3. 常见问题

### 3.1 导入错误

**问题：** 导入 `MListWidget` 时出错：
```
ImportError: cannot import name 'MListWidget' from 'dayu_widgets'
```

**解决方案：**
使用 `MListView` 替代 `MListWidget`。

### 3.2 主题不生效

**问题：** 组件样式不符合预期。

**解决方案：**
确保正确应用了 `dayu_theme`：
```python
from dayu_widgets import dayu_theme

app = QApplication.instance()
dayu_theme.apply(app)
```

## 4. 最佳实践

1. **统一导入风格**
   - 使用 `qtpy` 导入 Qt 基础组件
   - 从 `dayu_widgets` 导入 UI 组件

2. **组件命名**
   - 使用有意义的变量名
   - 为组件设置唯一的 `objectName` 以便样式定制

3. **布局管理**
   - 使用 `QVBoxLayout` 和 `QHBoxLayout` 进行基础布局
   - 使用 `MFlowLayout` 实现流式布局

4. **信号与槽**
   - 使用 `@QtCore.Slot()` 装饰器标记槽函数
   - 避免直接操作 UI 元素，通过信号通知更新

## 5. 参考链接

- [Qt 官方文档](https://doc.qt.io/)
- [dayu_widgets 文档](https://dayu.taobao.net/)
- [Maya Python API 文档](https://help.autodesk.com/view/MAYAUL/2023/ENU/)
