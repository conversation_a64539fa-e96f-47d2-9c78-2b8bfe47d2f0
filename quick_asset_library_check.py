"""Quick Asset Library Check.

快速检查毛发素材库数据显示问题的脚本。
在使用 debug_start_hair_dev_direct.cmd 启动环境后运行此脚本。
"""

import sys
import os

# Add the project root to the Python path
project_root = os.path.dirname(__file__)
sys.path.insert(0, project_root)

def quick_data_check():
    """快速检查数据是否可用。"""
    print("=" * 50)
    print("快速数据检查")
    print("=" * 50)
    
    try:
        # 检查 MockDataManager
        from cgame_avatar_factory.hair_studio.data.mock_data_manager import MockDataManager
        mock_manager = MockDataManager()
        
        all_assets = mock_manager.get_assets()
        print(f"✓ MockDataManager 总资产: {len(all_assets)}")
        
        if len(all_assets) == 0:
            print("❌ MockDataManager 没有资产数据!")
            return False
        
        # 检查各类型资产
        for asset_type in ["card", "xgen", "curve"]:
            assets = mock_manager.get_assets(asset_type)
            print(f"  - {asset_type}: {len(assets)} 个")
            if len(assets) == 0:
                print(f"❌ {asset_type} 类型没有资产!")
                return False
        
        print("✅ 数据层正常")
        return True
        
    except Exception as e:
        print(f"❌ 数据检查失败: {str(e)}")
        return False

def quick_hair_manager_check():
    """快速检查 HairManager。"""
    print("\n" + "=" * 50)
    print("HairManager 检查")
    print("=" * 50)
    
    try:
        from cgame_avatar_factory.hair_studio.manager.hair_manager import HairManager
        
        hair_manager = HairManager()
        print("✓ HairManager 创建成功")
        
        # 检查资产获取
        for asset_type in ["card", "xgen", "curve"]:
            assets = hair_manager.get_assets(asset_type)
            print(f"  - {asset_type}: {len(assets)} 个资产")
            if len(assets) == 0:
                print(f"❌ HairManager 无法获取 {asset_type} 资产!")
                return False, None
        
        print("✅ HairManager 正常")
        return True, hair_manager
        
    except Exception as e:
        print(f"❌ HairManager 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def quick_asset_library_check(hair_manager):
    """快速检查 AssetLibrary。"""
    print("\n" + "=" * 50)
    print("AssetLibrary 检查")
    print("=" * 50)
    
    try:
        from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import AssetLibrary
        
        # 测试 Card 类型的 AssetLibrary
        print("✓ 创建 Card AssetLibrary...")
        asset_library = AssetLibrary("card", hair_manager)
        
        print(f"✓ 初始资产数量: {len(asset_library.assets)}")
        
        if len(asset_library.assets) == 0:
            print("❌ AssetLibrary 初始化后没有资产!")
            print("✓ 尝试手动刷新...")
            asset_library.refresh()
            print(f"✓ 刷新后资产数量: {len(asset_library.assets)}")
            
            if len(asset_library.assets) == 0:
                print("❌ 手动刷新后仍然没有资产!")
                return False, None
        
        # 检查资产内容
        print("✓ 资产内容:")
        for asset in asset_library.assets:
            print(f"    * {asset['name']} (ID: {asset['id']})")
        
        # 检查 UI 组件
        print(f"✓ UI 网格组件数量: {asset_library.assets_layout.count()}")
        
        print("✅ AssetLibrary 正常")
        return True, asset_library
        
    except Exception as e:
        print(f"❌ AssetLibrary 检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False, None

def check_ui_display_issues(asset_library):
    """检查 UI 显示问题。"""
    print("\n" + "=" * 50)
    print("UI 显示问题检查")
    print("=" * 50)
    
    try:
        # 检查主要组件
        print(f"✓ AssetLibrary 可见性: {asset_library.isVisible()}")
        print(f"✓ AssetLibrary 启用状态: {asset_library.isEnabled()}")
        
        # 检查大小
        size = asset_library.size()
        print(f"✓ AssetLibrary 大小: {size.width()}x{size.height()}")
        
        if size.width() == 0 or size.height() == 0:
            print("❌ AssetLibrary 大小为 0!")
            return False
        
        # 检查容器
        container = asset_library.assets_container
        print(f"✓ 资产容器可见性: {container.isVisible()}")
        
        container_size = container.size()
        print(f"✓ 容器大小: {container_size.width()}x{container_size.height()}")
        
        # 检查布局
        layout = asset_library.assets_layout
        item_count = layout.count()
        print(f"✓ 布局项目数量: {item_count}")
        
        if item_count == 0:
            print("❌ 布局中没有项目!")
            print("✓ 尝试强制更新网格...")
            asset_library._update_assets_grid()
            new_count = layout.count()
            print(f"✓ 强制更新后项目数量: {new_count}")
            
            if new_count == 0:
                print("❌ 强制更新后仍然没有项目!")
                return False
        
        # 检查具体的资产项目
        print("✓ 检查资产项目:")
        for i in range(min(item_count, 3)):  # 只检查前3个
            item = layout.itemAt(i)
            if item and item.widget():
                widget = item.widget()
                print(f"    项目 {i}: {widget.__class__.__name__}")
                print(f"      可见性: {widget.isVisible()}")
                print(f"      大小: {widget.size().width()}x{widget.size().height()}")
        
        print("✅ UI 显示检查完成")
        return True
        
    except Exception as e:
        print(f"❌ UI 显示检查失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def suggest_solutions():
    """提供解决方案建议。"""
    print("\n" + "=" * 50)
    print("可能的解决方案")
    print("=" * 50)
    
    print("如果上述检查都通过但仍然看不到资产，可能的原因:")
    print()
    print("1. 🔍 窗口大小问题:")
    print("   - 尝试调整 Hair Studio 窗口大小")
    print("   - 检查是否需要滚动才能看到资产")
    print()
    print("2. 🎨 样式表问题:")
    print("   - 可能有 CSS 样式隐藏了资产项目")
    print("   - 尝试切换 Maya 主题")
    print()
    print("3. 🔄 刷新时机问题:")
    print("   - 尝试手动切换标签页 (Card/XGen/Curve)")
    print("   - 关闭并重新打开 Hair Studio")
    print()
    print("4. 📐 布局问题:")
    print("   - 检查 AssetLibrary 是否被其他组件遮挡")
    print("   - 尝试调整面板分割器")
    print()
    print("5. 🔧 强制刷新:")
    print("   - 在 Hair Studio 中右键点击资产库区域")
    print("   - 尝试搜索功能来触发刷新")

def main():
    """主要诊断函数。"""
    print("🔍 毛发素材库快速诊断")
    print("请确保已使用 debug_start_hair_dev_direct.cmd 启动环境")
    print()
    
    # 快速数据检查
    if not quick_data_check():
        print("\n❌ 数据层有问题，请检查 MockDataManager")
        return
    
    # HairManager 检查
    success, hair_manager = quick_hair_manager_check()
    if not success:
        print("\n❌ HairManager 有问题")
        return
    
    # AssetLibrary 检查
    success, asset_library = quick_asset_library_check(hair_manager)
    if not success:
        print("\n❌ AssetLibrary 有问题")
        return
    
    # UI 显示检查
    if not check_ui_display_issues(asset_library):
        print("\n❌ UI 显示有问题")
    
    # 提供解决方案
    suggest_solutions()
    
    print("\n" + "=" * 50)
    print("诊断完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
