"""Base Hair Tab Module.

This module provides the base class for all hair type tabs in the Hair Studio tool.
"""

# Import standard library
import logging

# Import Qt modules
from qtpy import QtWidgets, QtCore

# Import local modules
from cgame_avatar_factory.hair_studio.ui.component_list import ComponentList
from cgame_avatar_factory.hair_studio.ui.editor_area import <PERSON><PERSON><PERSON>
from cgame_avatar_factory.hair_studio.ui.asset_library.asset_library import Asset<PERSON><PERSON><PERSON>
from cgame_avatar_factory.hair_studio.constants import (
    STRETCH_FACTOR_ASSET_LIBRARY,
    STRETCH_FACTOR_COMPONENT_LIST,
    STRETCH_FACTOR_EDITOR_AREA,
    LAYOUT_MARGIN_ZERO,
    LAYOUT_SPACING_SMALL,
    ERROR_MSG_ERROR_CREATING_COMPONENT,
    ERROR_MSG_ERROR_SELECTING_COMPONENT,
    ERROR_MSG_ERROR_DELETING_COMPONENT,
    ERROR_MSG_ERROR_REFRESHING_ASSET_LIBRARY,
    ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST,
    ERROR_MSG_ERROR_SETTING_SELECTED_COMPONENT,
    ERROR_MSG_FAILED_TO_CONNECT_SIGNALS,
    ERROR_MSG_FAILED_TO_CREATE_COMPONENT,
)


class BaseHairTab(QtWidgets.QWidget):
    """Base class for hair type tabs.

    This class provides common functionality for all hair type tabs (Card, XGen, Curve).
    Each tab contains three main areas: editor area, component list, and asset library.
    """

    def __init__(self, hair_type, hair_manager, parent=None, logger=None):
        """Initialize the BaseHairTab.

        Args:
            hair_type (str): Type of hair ('card', 'xgen', 'curve')
            hair_manager (HairManager): The hair manager instance
            parent (QWidget, optional): The parent widget. Defaults to None.
            logger (logging.Logger, optional): Logger instance to use. If None, creates a new one.
        """
        super(BaseHairTab, self).__init__(parent)
        self.hair_type = hair_type
        self._hair_manager = hair_manager
        self._selected_component = None
        self.object_name = "{}HairTab".format(hair_type.capitalize())
        self.setObjectName(self.object_name)

        # Initialize logger - use provided logger or create new one
        self._logger = logger if logger is not None else logging.getLogger(__name__)

        # Flag to prevent signal recursion
        self._updating_selection = False

        # Initialize UI components
        self.setup_ui()

        # Connect signals
        self._connect_signals()

        # Initial refresh (moved after UI setup)
        # Refresh is now handled by the setup_ui method

    def setup_ui(self):
        """Set up the user interface components."""
        try:
            # Main layout
            main_layout = QtWidgets.QHBoxLayout(self)
            main_layout.setContentsMargins(
                LAYOUT_MARGIN_ZERO,
                LAYOUT_MARGIN_ZERO,
                LAYOUT_MARGIN_ZERO,
                LAYOUT_MARGIN_ZERO,
            )
            main_layout.setSpacing(LAYOUT_SPACING_SMALL)

            # Create main areas with the shared hair manager and logger
            self.editor_area = EditorArea(
                self.hair_type, self._hair_manager, self, logger=self._logger
            )
            self.component_list = ComponentList(
                self.hair_type, self._hair_manager, self, logger=self._logger
            )
            self.asset_library = AssetLibrary(
                self.hair_type, self._hair_manager, self, logger=self._logger
            )

            # Create horizontal splitter for resizable panels
            self.main_splitter = QtWidgets.QSplitter(QtCore.Qt.Horizontal)
            self.main_splitter.setChildrenCollapsible(
                False
            )  # Prevent panels from collapsing completely

            # Add the three main components to splitter
            self.main_splitter.addWidget(self.editor_area)
            self.main_splitter.addWidget(self.component_list)
            self.main_splitter.addWidget(self.asset_library)

            # Set initial sizes (40%, 30%, 30%)
            self.main_splitter.setSizes(
                [
                    STRETCH_FACTOR_EDITOR_AREA * 100,
                    STRETCH_FACTOR_COMPONENT_LIST * 100,
                    STRETCH_FACTOR_ASSET_LIBRARY * 100,
                ]
            )

            # Set minimum sizes to prevent panels from becoming too small
            self.editor_area.setMinimumWidth(200)
            self.component_list.setMinimumWidth(150)
            self.asset_library.setMinimumWidth(150)

            # Add splitter to main layout
            main_layout.addWidget(self.main_splitter)

            # Initial refresh after UI is set up
            self.refresh_asset_library()

        except Exception as e:
            self._logger.error("Failed to set up UI: %s", str(e), exc_info=True)
            QtWidgets.QMessageBox.critical(
                self, "Error", "Failed to initialize tab UI: {}".format(str(e))
            )

    def _connect_signals(self):
        """Connect signals between components."""
        try:
            # When an asset is selected in the library, create a new component
            self.asset_library.asset_selected.connect(self._on_asset_selected)

            # When a component is selected in the list, update the editor area
            self.component_list.component_selected.connect(self._on_component_selected)

            # When a component is deleted from the list
            self.component_list.component_deleted.connect(self._on_component_deleted)

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_FAILED_TO_CONNECT_SIGNALS, str(e), exc_info=True
            )

    def _on_asset_selected(self, asset_id):
        """Handle asset selection from the asset library.

        Args:
            asset_id (str): ID of the selected asset
        """
        try:
            # Create a new component from the selected asset
            component = self._hair_manager.create_component(asset_id)
            if component:
                # Select the new component
                self._hair_manager.select_component(component.id)

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_CREATING_COMPONENT, str(e), exc_info=True
            )
            QtWidgets.QMessageBox.critical(
                self,
                "Error",
                "{}: {}".format(ERROR_MSG_FAILED_TO_CREATE_COMPONENT, str(e)),
            )

    def _on_component_selected(self, component_id):
        """Handle component selection from the component list.

        Args:
            component_id (str): ID of the selected component
        """
        try:
            # Prevent recursion
            if self._updating_selection:
                return

            self._updating_selection = True

            # Update the hair manager's selected component
            self._hair_manager.select_component(component_id)

            # Get the component details
            component = self._hair_manager.get_component(component_id)
            if component:
                # Update the editor area with the component details
                self.editor_area.set_component(component)

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_SELECTING_COMPONENT, str(e), exc_info=True
            )
        finally:
            self._updating_selection = False

    def _on_component_deleted(self, component_id):
        """Handle component deletion from the component list.

        Args:
            component_id (str): ID of the component to delete
        """
        try:
            # Delete the component through the hair manager
            if not self._hair_manager.delete_component(component_id):
                self._logger.warning("Failed to delete component: %s", component_id)

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_DELETING_COMPONENT, str(e), exc_info=True
            )

    def refresh_asset_library(self):
        """Refresh the asset library with the latest data."""
        try:
            # Get assets filtered by the current hair type
            assets = self._hair_manager.get_assets(asset_type=self.hair_type)

            # Update the asset library
            self.asset_library.update_assets(assets)

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_REFRESHING_ASSET_LIBRARY,
                str(e),
                exc_info=True,
            )

    def update_component_list(self, components):
        """Update the component list with the given components.

        Args:
            components (list): List of HairComponent objects
        """
        try:
            # Filter components by the current hair type
            filtered_components = [
                comp for comp in components if comp.get("type") == self.hair_type
            ]

            # Update the component list (this won't emit signals)
            self.component_list.update_components(filtered_components)

            # Explicitly clear selection to avoid signal loops
            self.component_list.clear_selection()

        except Exception as e:
            self._logger.error(
                "%s: %s", ERROR_MSG_ERROR_UPDATING_COMPONENT_LIST, str(e), exc_info=True
            )

    def set_selected_component(self, component):
        """Set the currently selected component.

        Args:
            component (HairComponent or None): The component to select, or None to clear selection
        """
        try:
            # Prevent recursion
            if self._updating_selection:
                return

            self._updating_selection = True
            self._selected_component = component

            if component:
                # Update the editor area
                self.editor_area.set_component(component)

                # Select the component in the list
                self.component_list.select_component(component.id)
            else:
                # Clear the editor area by setting component to None
                self.editor_area.set_component(None)

                # Clear the selection in the list
                self.component_list.clear_selection()

        except Exception as e:
            self._logger.error(
                "%s: %s",
                ERROR_MSG_ERROR_SETTING_SELECTED_COMPONENT,
                str(e),
                exc_info=True,
            )
        finally:
            self._updating_selection = False

    def get_hair_type(self):
        """Get the hair type of this tab.

        Returns:
            str: The hair type ('card', 'xgen', or 'curve')
        """
        return self.hair_type

    def get_hair_manager(self):
        """Get the hair manager instance.

        Returns:
            HairManager: The hair manager instance
        """
        return self._hair_manager
